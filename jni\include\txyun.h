#ifndef TXYUN_H
#define TXYUN_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <curl/curl.h>
#include <time.h>
#include <stdarg.h>

#define INITIAL_SIZE 163840 // 初始大小 160KB
#define MAX_SIZE 1048576    // 最大限制 1MB
#define COMMAND_SIZE 8192   // 8KB

// 腾讯云分享链接和版本配置
#define TXYUN_URL "https://sharechain.qq.com/9cfa0446d750d3fbdc9305f4eeee2a5e"
#define TXYUN_VERSION "1.0"

// 数据写入结构体
struct WriteData
{
    char *buffer;
    size_t size;
    size_t capacity;
};

// 从两个标记之间提取内容
static const char *getSubstring(const char *str, const char *start_tag, const char *end_tag)
{
    static char result[INITIAL_SIZE];
    const char *start = strstr(str, start_tag);
    if (start)
    {
        start += strlen(start_tag);
        const char *end = strstr(start, end_tag);
        if (end)
        {
            int length = end - start;
            if (length > 0 && length < INITIAL_SIZE)
            {
                strncpy(result, start, length);
                result[length] = '\0';
                return result;
            }
        }
    }
    return NULL;
}

// 简单的JSON值提取函数
static const char* extractJsonValue(const char* json, const char* key, char* output, int max_len)
{
    if (!json || !key || !output) return NULL;

    // 构造查找模式 "key":"
    char pattern[256];
    snprintf(pattern, sizeof(pattern), "\"%s\":\"", key);

    const char* start = strstr(json, pattern);
    if (!start) return NULL;

    start += strlen(pattern); // 跳过 "key":"

    // 查找结束的引号
    const char* end = strchr(start, '"');
    if (!end) return NULL;

    int len = end - start;
    if (len <= 0 || len >= max_len) return NULL;

    strncpy(output, start, len);
    output[len] = '\0';

    return output;
}

// 从brief中解析JSON格式的版本、哈希值和直链
static bool parseBrief(const char *brief, char *version, char *hash, char *url)
{
    if (!brief || !version || !hash || !url)
    {
        //printf("[-] parseBrief: 参数为空\n");
        return false;
    }

    printf("[+] 开始解析brief: %s\n", brief);

    // 查找 version=
    const char *version_start = strstr(brief, "version=");
    if (!version_start)
    {
        //printf("[-] parseBrief: 找不到 'version='\n");
        return false;
    }
    version_start += strlen("version="); // 跳过 "version="

    // 查找版本号结束位置（换行或字符串结束）
    const char *version_end = version_start;
    while (*version_end && *version_end != '\n' && *version_end != '\r')
    {
        version_end++;
    }

    // 提取版本号
    int version_len = version_end - version_start;
    if (version_len <= 0 || version_len >= 1024)
    {
        //printf("[-] parseBrief: 版本号长度无效 (%d)\n", version_len);
        return false;
    }
    strncpy(version, version_start, version_len);
    version[version_len] = '\0';
    printf("[+] 提取到版本号: %s (长度: %d)\n", version, version_len);

    // 查找 hash=
    const char *hash_start = strstr(brief, "hash=");
    if (!hash_start)
    {
        //printf("[-] parseBrief: 找不到 'hash='\n");
        return false;
    }
    hash_start += strlen("hash="); // 跳过 "hash="

    // 查找哈希值结束位置（空格或字符串结束）
    const char *hash_end = hash_start;
    while (*hash_end && *hash_end != ' ' && *hash_end != '\t' && *hash_end != '\n' && *hash_end != '\r')
    {
        hash_end++;
    }

    // 提取哈希值
    int hash_len = hash_end - hash_start;
    if (hash_len <= 0 || hash_len >= 1024)
    {
        //printf("[-] parseBrief: 哈希长度无效 (%d)\n", hash_len);
        return false;
    }
    strncpy(hash, hash_start, hash_len);
    hash[hash_len] = '\0';
    printf("[+] 提取到哈希值: %s (长度: %d)\n", hash, hash_len);

    // 查找 url=
    const char *url_start = strstr(brief, "url=");
    if (!url_start)
    {
        //printf("[-] parseBrief: 找不到 'url='\n");
        return false;
    }
    url_start += strlen("url="); // 跳过 "url="

    // 查找URL结束位置（空格或字符串结束）
    const char *url_end = url_start;
    while (*url_end && *url_end != ' ' && *url_end != '\t' && *url_end != '\n' && *url_end != '\r')
    {
        url_end++;
    }

    // 提取URL
    int url_len = url_end - url_start;
    if (url_len <= 0 || url_len >= 1024)
    {
        //printf("[-] parseBrief: URL长度无效 (%d)\n", url_len);
        return false;
    }
    strncpy(url, url_start, url_len);
    url[url_len] = '\0';
    printf("[+] 提取到URL: %s (长度: %d)\n", url, url_len);

    return true;
}

// 提取title和brief到传入的缓冲区
static bool extractTitleAndBrief(const char *json, char *title, char *brief)
{
    const char *title_start = strstr(json, "\"title\":\"");
    const char *brief_start = strstr(json, "\"brief\":\"");

    if (title_start && brief_start)
    {
        title_start += strlen("\"title\":\"");
        brief_start += strlen("\"brief\":\"");

        const char *title_end = strchr(title_start, '"');
        const char *brief_end = strchr(brief_start, '"');

        if (title_end && brief_end)
        {
            int title_len = title_end - title_start;
            strncpy(title, title_start, title_len);
            title[title_len] = '\0';

            int brief_len = brief_end - brief_start;
            strncpy(brief, brief_start, brief_len);
            brief[brief_len] = '\0';

            return true;
        }
    }
    return false;
}

// 初始化WriteData结构
static bool init_write_data(struct WriteData *data)
{
    data->buffer = (char *)malloc(INITIAL_SIZE);
    if (!data->buffer)
    {
        return false;
    }
    data->size = 0;
    data->capacity = INITIAL_SIZE;
    data->buffer[0] = '\0';
    return true;
}

// 释放WriteData结构
static void free_write_data(struct WriteData *data)
{
    if (data && data->buffer)
    {
        free(data->buffer);
        data->buffer = NULL;
        data->size = 0;
        data->capacity = 0;
    }
}

// 扩展缓冲区
static bool expand_buffer(struct WriteData *data, size_t needed_size)
{
    if (!data || !data->buffer)
        return false;

    size_t new_size = data->capacity;
    while (new_size < needed_size)
    {
        new_size *= 2;
        if (new_size > MAX_SIZE)
        {
            return false;
        }
    }

    char *new_buffer = (char *)realloc(data->buffer, new_size);
    if (!new_buffer)
    {
        return false;
    }

    data->buffer = new_buffer;
    data->capacity = new_size;
    return true;
}

// 写入回调函数
static size_t WriteCallback(const char *ptr, size_t size, size_t nmemb, void *userdata)
{
    if (!ptr || !userdata)
    {
        return 0;
    }

    size_t realsize = size * nmemb;
    struct WriteData *data = (struct WriteData *)userdata;

    if (!data->buffer)
    {
        return 0;
    }

    if (data->size + realsize + 1 > data->capacity)
    {
        if (!expand_buffer(data, data->size + realsize + 1))
        {
            return 0;
        }
    }

    memcpy(data->buffer + data->size, ptr, realsize);
    data->size += realsize;
    data->buffer[data->size] = '\0';

    return realsize;
}

// 获取腾讯云分享内容
static bool getTxyunContent(const char *url, char *title, char *brief)
{
    if (!url || !title || !brief)
    {
        //printf("[-] getTxyunContent: 参数为空\n");
        return false;
    }

    //printf("[+] 开始获取腾讯云内容，URL: %s\n", url);

    CURL *curl;
    CURLcode res;
    struct WriteData data = {0};

    if (!init_write_data(&data))
    {
        //printf("[-] 初始化写入数据结构失败\n");
        return false;
    }

    res = curl_global_init(CURL_GLOBAL_DEFAULT);
    if (res != CURLE_OK)
    {
        //printf("[-] curl_global_init 失败: %s\n", curl_easy_strerror(res));
        free_write_data(&data);
        return false;
    }

    curl = curl_easy_init();
    if (!curl)
    {
        //printf("[-] curl_easy_init 失败\n");
        curl_global_cleanup();
        free_write_data(&data);
        return false;
    }

    //printf("[+] CURL 初始化成功\n");

    // 检查 CURL 版本和支持的协议
    curl_version_info_data *version_info = curl_version_info(CURLVERSION_NOW);
    //printf("[+] CURL 版本: %s\n", version_info->version);
    //printf("[+] 支持的协议: %s\n", version_info->protocols[0] ? "有协议支持" : "无协议支持");

    // 打印所有支持的协议
    for (int i = 0; version_info->protocols[i]; i++) {
        //printf("    协议 %d: %s\n", i, version_info->protocols[i]);
    }

    size_t (*write_cb)(const char *, size_t, size_t, void *) = WriteCallback;

    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_cb);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&data);
    curl_easy_setopt(curl, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);
    curl_easy_setopt(curl, CURLOPT_ACCEPT_ENCODING, "");

    //printf("[+] 开始执行 HTTP 请求...\n");
    res = curl_easy_perform(curl);

    bool result = false;
    if (res == CURLE_OK)
    {
        //printf("[+] HTTP 请求成功，接收到 %zu 字节数据\n", data.size);
        if (data.size > 0)
        {
            //printf("[+] 开始解析页面内容...\n");
            const char *start_tag = "<script type=\"text/javascript\">window.syncData = ";
            const char *end_tag = ";</script><script>";
            const char *syncData = getSubstring(data.buffer, start_tag, end_tag);

            if (syncData)
            {
                //printf("[+] 找到 syncData，开始提取 title 和 brief\n");
                result = extractTitleAndBrief(syncData, title, brief);
                if (result)
                {
                    //printf("[+] 成功提取 title 和 brief\n");
                }
                else
                {
                    //printf("[-] 提取 title 和 brief 失败\n");
                }
            }
            else
            {
                //printf("[-] 未找到 syncData 标签\n");
                //printf("[DEBUG] 页面内容前500字符: %.500s\n", data.buffer);
            }
        }
        else
        {
            //printf("[-] 接收到的数据为空\n");
        }
    }
    else
    {
        //printf("[-] HTTP 请求失败: %s\n", curl_easy_strerror(res));
    }

    curl_easy_cleanup(curl);
    curl_global_cleanup();
    free_write_data(&data);

    return result;
}

// 获取版本、哈希和直链
static bool getTxyunVersionHashUrl(const char *url, char *version, char *hash, char *download_url)
{
    if (!url || !version || !hash || !download_url)
    {
        return false;
    }

    char title[1024] = {0};
    char brief[1024] = {0};

    // 先获取原始的title和brief
    //printf("[+] 开始获取腾讯云版本、哈希和URL\n");
    if (!getTxyunContent(url, title, brief))
    {
        //printf("[-] 获取腾讯云内容失败\n");
        return false;
    }

    printf("[+] 原始数据:\n");
    printf("    title: %s (现在用作备注)\n", title);
    printf("    brief: %s\n", brief);

    // 从brief中解析版本、哈希和URL
    if (!parseBrief(brief, version, hash, download_url))
    {
        //printf("[-] 解析brief失败\n");
        return false;
    }

    printf("[+] 解析结果:\n");
    printf("    版本号: %s\n", version);
    printf("    备注: %s\n", title);
    printf("    哈希值: %s\n", hash);
    printf("    下载链接: %s\n", download_url);

    return true;
}

#endif // TXYUN_H